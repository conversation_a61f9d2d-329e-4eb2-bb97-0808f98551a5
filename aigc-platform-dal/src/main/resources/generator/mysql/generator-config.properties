base.author=<EMAIL>
base.version=v1.0
base.enableLogicalDel=1

ds.driver=com.mysql.cj.jdbc.Driver
ds.url=*****************************************
ds.user=root
ds.pwd=admin123

#dal
proj.entity=aigc-platform-dal/src/main/java
proj.dao=aigc-platform-dal/src/main/java
proj.example=aigc-platform-dal/src/main/java
proj.sqlmap=aigc-platform-dal/src/main/resources

pkg.entity=ai.conrain.aigc.platform.dal.entity
pkg.dao=ai.conrain.aigc.platform.dal.dao
pkg.example=ai.conrain.aigc.platform.dal.example
pkg.sqlmap=sqlmap

#service
proj.service=aigc-platform-service/src/main/java
proj.serviceImpl=aigc-platform-service/src/main/java
proj.vo=aigc-platform-service/src/main/java
proj.converter=aigc-platform-service/src/main/java
proj.query=aigc-platform-service/src/main/java

pkg.service=ai.conrain.aigc.platform.service.component
pkg.serviceImpl=ai.conrain.aigc.platform.service.component.impl
pkg.converter=ai.conrain.aigc.platform.service.model.converter
pkg.vo=ai.conrain.aigc.platform.service.model.vo
pkg.query=ai.conrain.aigc.platform.service.model.query

#web
proj.controller=aigc-platform-web/src/main/java

pkg.controller=ai.conrain.aigc.platform.web.controller



