-- 启用vector插件
CREATE EXTENSION vector;

-- 20250723 agent项目
DROP TABLE IF EXISTS image;
CREATE TABLE image (
    id SERIAL PRIMARY KEY,
    type VARCHAR(32),
    url VARCHAR(1024) NOT NULL,
    show_img_url VARCHAR(1024),
    image_path VARCHAR,
    image_hash VARCHAR,
    metadata JSONB,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 表注释
COMMENT ON TABLE image IS '图像基本信息表';

-- 字段注释
COMMENT ON COLUMN image.id IS '图像ID，自增主键';
COMMENT ON COLUMN image.url IS '图像原始URL地址';
COMMENT ON COLUMN image.show_img_url IS '展示图像URL地址';
COMMENT ON COLUMN image.type IS '图像类型，用于区分服装图、风格示意图';
COMMENT ON COLUMN image.image_path IS '图片路径';
COMMENT ON COLUMN image.image_hash IS '图片内容哈希';
COMMENT ON COLUMN image.metadata IS '图像元数据，JSON格式';
COMMENT ON COLUMN image.ext_info IS '扩展信息，JSON格式';
COMMENT ON COLUMN image.create_time IS '创建时间';
COMMENT ON COLUMN image.modify_time IS '修改时间';
COMMENT ON COLUMN image.deleted IS '软删除标记';

-- 在type字段上创建索引，加速按类型查询
CREATE INDEX idx_image_type ON image(type) WHERE deleted = FALSE;

-- 在create_time上创建索引，加速按时间范围查询
CREATE INDEX idx_image_create_time ON image(create_time);

-- 创建图像标注表
DROP TABLE IF EXISTS image_caption;
CREATE TABLE image_caption (
    id SERIAL PRIMARY KEY,
    image_id INTEGER NOT NULL,
    image_type VARCHAR,
    caption JSONB,
    caption_version VARCHAR(16),
    cloth_gender_type VARCHAR,

    img_emb VECTOR(1024),
    bg_img_emb VECTOR(1024),
    model_facial_img_emb VECTOR(1024),
    model_pose_img_emb VECTOR(1024),

    cloth_style_text_emb VECTOR(256),
    cloth_text_emb VECTOR(256),
    bg_text_emb VECTOR(256),
    accessories_text_emb VECTOR(256),
    hairstyle_text_emb VECTOR(256),
    pose_text_emb VECTOR(256),

    sort_bg_text_emb VECTOR(1024),
    sort_facial_expression_text_emb VECTOR(1024),
    sort_accessories_text_emb VECTOR(1024),
    sort_pose_text_emb VECTOR(1024),

    ext_info VARCHAR(4096),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 表注释
COMMENT ON TABLE image_caption IS '图像标注表，存储图像的多模态特征向量及标注文本，用于内容检索和分类';

-- 字段注释
COMMENT ON COLUMN image_caption.id IS '自增主键ID';
COMMENT ON COLUMN image_caption.image_id IS '关联的图像ID，用于跨表查询';
COMMENT ON COLUMN image_caption.image_type IS '图像类型，如服装图、风格示意图';
COMMENT ON COLUMN image_caption.caption IS '图像标注的完整文本描述，JSON格式存储';
COMMENT ON COLUMN image_caption.caption_version IS '标注版本号，用于区分不同标注模型或规则';
COMMENT ON COLUMN image_caption.cloth_gender_type IS '服装性别类型，如男装、女装、通用';
COMMENT ON COLUMN image_caption.img_emb IS '整图特征向量（1024维）';
COMMENT ON COLUMN image_caption.bg_img_emb IS '背景抠图特征向量（1024维）';
COMMENT ON COLUMN image_caption.model_facial_img_emb IS '模特面部抠图特征向量（1024维）';
COMMENT ON COLUMN image_caption.model_pose_img_emb IS '模特姿势抠图特征向量（1024维）';
COMMENT ON COLUMN image_caption.cloth_style_text_emb IS '服装款式描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.cloth_text_emb IS '服装整体描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.bg_text_emb IS '背景道具描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.accessories_text_emb IS '配饰描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.hairstyle_text_emb IS '发型描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.pose_text_emb IS '姿势描述文本向量（1024维）';
COMMENT ON COLUMN image_caption.sort_bg_text_emb IS '背景分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.sort_facial_expression_text_emb IS '表情分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.sort_accessories_text_emb IS '配饰分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.sort_pose_text_emb IS '姿势分类文本向量（1024维，用于聚类或检索）';
COMMENT ON COLUMN image_caption.ext_info IS '扩展信息，JSON格式存储非结构化附加数据';
COMMENT ON COLUMN image_caption.create_time IS '记录创建时间，默认为当前时间戳';
COMMENT ON COLUMN image_caption.modify_time IS '记录最后修改时间，默认为当前时间戳';
COMMENT ON COLUMN image_caption.deleted IS '软删除标记，FALSE表示未删除，TRUE表示已删除';
---------------------------------- 标注表 ----------------------------------

DROP INDEX IF EXISTS idx_image_caption_image_id;
CREATE INDEX idx_image_caption_image_id ON image_caption(image_id);

DROP INDEX IF EXISTS idx_caption_version;
CREATE INDEX idx_caption_version ON image_caption(caption_version);

DROP INDEX IF EXISTS idx_create_time_filtered;
CREATE INDEX idx_create_time_filtered ON image_caption(create_time);

DROP INDEX IF EXISTS idx_image_caption_image_type;
CREATE INDEX idx_image_caption_image_type ON image_caption(image_type);

DROP INDEX IF EXISTS idx_image_caption_cloth_gender_type;
CREATE INDEX idx_image_caption_cloth_gender_type ON image_caption(cloth_gender_type);

drop index if exists idx_genre;
CREATE index idx_genre ON image_caption ((caption -> 'Shooting_Theme' ->> 'Intended_Use'));

-- 为过滤条件创建B-tree索引
DROP INDEX IF EXISTS idx_filter_conditions;
CREATE INDEX idx_filter_conditions ON image_caption(image_type, cloth_gender_type, deleted)
WHERE cloth_style_text_emb IS NOT NULL;

DROP INDEX IF EXISTS idx_hnsw_emb_cloth_style;
CREATE INDEX idx_hnsw_emb_cloth_style ON image_caption
USING hnsw ((cloth_style_text_emb::vector(256)) vector_ip_ops)
WITH (m=32, ef_construction=200);

-- 更新统计信息
VACUUM ANALYZE image_caption;

---------------------------------- 标注表 ----------------------------------

CREATE TABLE IF NOT EXISTS image_caption_user (
  id SERIAL PRIMARY KEY,
  image_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  original_id INTEGER,
  caption JSONB,
  caption_version VARCHAR,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_caption_user IS '图像标注表用户标注数据，存储用户和大模型打标的数据';
COMMENT ON COLUMN image_caption_user.id IS '主键';
COMMENT ON COLUMN image_caption_user.image_id IS '图片ID';
COMMENT ON COLUMN image_caption_user.user_id IS '用户ID';
COMMENT ON COLUMN image_caption_user.original_id IS '对原始的image_caption_user记录进行修改';
COMMENT ON COLUMN image_caption_user.caption IS '标注';
COMMENT ON COLUMN image_caption_user.caption_version IS '标注版本';
COMMENT ON COLUMN image_caption_user.create_time IS '创建时间';
COMMENT ON COLUMN image_caption_user.modify_time IS '修改时间';
COMMENT ON COLUMN image_caption_user.deleted IS '是否删除';

-- 复合索引
CREATE INDEX idx_image_caption_user_image_id_user_id ON image_caption_user(image_id, user_id) WHERE deleted = FALSE;

CREATE TABLE IF NOT EXISTS image_group (
  id SERIAL PRIMARY KEY,
  image_ids JSONB,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_group IS '图像组，由多种图组成的pair对';
COMMENT ON COLUMN image_group.id IS '主键';
COMMENT ON COLUMN image_group.image_ids IS '图片ID列表';
COMMENT ON COLUMN image_group.create_time IS '创建时间';
COMMENT ON COLUMN image_group.modify_time IS '修改时间';
COMMENT ON COLUMN image_group.deleted IS '是否删除';

CREATE TABLE IF NOT EXISTS image_group_caption (
  id SERIAL PRIMARY KEY,
  image_group_id INTEGER NOT NULL,
  image_caption_ids JSONB,
  status VARCHAR NOT NULL DEFAULT 'pending',
  caption JSONB,
  caption_log JSONB,
  caption_version VARCHAR,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_group_caption IS '图像组标注表，pair对打标的最终数据';
COMMENT ON COLUMN image_group_caption.id IS '主键';
COMMENT ON COLUMN image_group_caption.image_group_id IS '图片组ID';
COMMENT ON COLUMN image_group_caption.image_caption_ids IS '图片标注ID列表';
COMMENT ON COLUMN image_group_caption.status IS '状态';
COMMENT ON COLUMN image_group_caption.caption IS '标注';
COMMENT ON COLUMN image_group_caption.caption_log IS '标注日志';
COMMENT ON COLUMN image_group_caption.caption_version IS '标注版本';
COMMENT ON COLUMN image_group_caption.create_time IS '创建时间';
COMMENT ON COLUMN image_group_caption.modify_time IS '修改时间';
COMMENT ON COLUMN image_group_caption.deleted IS '是否删除';

CREATE INDEX idx_image_group_caption_image_group_id ON image_group_caption(image_group_id) WHERE deleted = FALSE;

CREATE TABLE IF NOT EXISTS image_group_caption_user (
  id SERIAL PRIMARY KEY,
  image_group_id INTEGER NOT NULL,
  user_id INTEGER NOT NULL,
  caption JSONB,
  caption_version VARCHAR,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted BOOLEAN NOT NULL DEFAULT FALSE
);
COMMENT ON TABLE image_group_caption_user IS '图像组标注表用户标注数据，多人打标';
COMMENT ON COLUMN image_group_caption_user.id IS '主键';
COMMENT ON COLUMN image_group_caption_user.image_group_id IS '图片组ID';
COMMENT ON COLUMN image_group_caption_user.user_id IS '用户ID';
COMMENT ON COLUMN image_group_caption_user.caption IS '标注';
COMMENT ON COLUMN image_group_caption_user.caption_version IS '标注版本';
COMMENT ON COLUMN image_group_caption_user.create_time IS '创建时间';
COMMENT ON COLUMN image_group_caption_user.modify_time IS '修改时间';
COMMENT ON COLUMN image_group_caption_user.deleted IS '是否删除';

CREATE INDEX idx_image_group_caption_user_image_group_id_user_id ON image_group_caption_user(image_group_id, user_id) WHERE deleted = FALSE;
