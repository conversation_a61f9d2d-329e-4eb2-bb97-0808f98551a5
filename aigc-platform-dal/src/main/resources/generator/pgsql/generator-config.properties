base.author=<EMAIL>
base.version=v1.0
base.enableLogicalDel=1

ds.driver=org.postgresql.Driver
ds.url=*************************************************
ds.user=postgres
ds.pwd=

#dal
proj.entity=aigc-platform-dal/src/main/java
proj.dao=aigc-platform-dal/src/main/java
proj.example=aigc-platform-dal/src/main/java
proj.sqlmap=aigc-platform-dal/src/main/resources

pkg.entity=ai.conrain.aigc.platform.dal.pgsql.entity
pkg.dao=ai.conrain.aigc.platform.dal.pgsql.dao
pkg.example=ai.conrain.aigc.platform.dal.example
pkg.sqlmap=sqlmap4pgsql

#service
proj.service=aigc-platform-service/src/main/java
proj.serviceImpl=aigc-platform-service/src/main/java
proj.vo=aigc-platform-service/src/main/java
proj.converter=aigc-platform-service/src/main/java
proj.query=aigc-platform-service/src/main/java

pkg.service=ai.conrain.aigc.platform.service.component
pkg.serviceImpl=ai.conrain.aigc.platform.service.component.impl
pkg.converter=ai.conrain.aigc.platform.service.model.converter
pkg.vo=ai.conrain.aigc.platform.service.model.vo
pkg.query=ai.conrain.aigc.platform.service.model.query

#web
proj.controller=aigc-platform-web/src/main/java

pkg.controller=ai.conrain.aigc.platform.web.controller



