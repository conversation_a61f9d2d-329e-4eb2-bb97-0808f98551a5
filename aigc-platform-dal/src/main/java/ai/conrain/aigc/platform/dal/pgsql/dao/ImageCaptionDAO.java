package ai.conrain.aigc.platform.dal.pgsql.dao;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import com.pgvector.PGvector;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ImageCaptionDAO {
    long countByExample(ImageCaptionExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ImageCaptionDO record);

    int insertSelective(ImageCaptionDO record);

    List<ImageCaptionDO> selectByExample(ImageCaptionExample example);

    ImageCaptionDO selectByPrimaryKey(Integer id);

    ImageCaptionDO selectByPrimaryKeyWithLogicalDelete(@Param("id") Integer id, @Param("andLogicalDeleted") boolean andLogicalDeleted);

    int updateByExampleSelective(@Param("record") ImageCaptionDO record, @Param("example") ImageCaptionExample example);

    int updateByExample(@Param("record") ImageCaptionDO record, @Param("example") ImageCaptionExample example);

    int updateByPrimaryKeySelective(ImageCaptionDO record);

    int updateByPrimaryKey(ImageCaptionDO record);

    int logicalDeleteByExample(@Param("example") ImageCaptionExample example);

    int logicalDeleteByPrimaryKey(Integer id);

    /**
     * 根据服装款式向量相似度查询图像标注
     *
     * @param styleVector 服装款式向量
     * @param limit       返回结果数量限制
     * @param genre
     * @return 相似度匹配的图像标注列表
     */
    List<ImageCaptionDO> selectByStyleVector(@Param("styleVector") PGvector styleVector,
                                             @Param("gender") String gender,
                                             @Param("similarityThreshold") Double similarityThreshold,
                                             @Param("limit") Integer limit, String genre);
}