package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.dal.example.ImageCaptionExample;
import ai.conrain.aigc.platform.dal.pgsql.dao.ImageCaptionDAO;
import ai.conrain.aigc.platform.dal.pgsql.entity.ImageCaptionDO;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.aliyun.AliyunEmbeddingService;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.converter.ImageCaptionConverter;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ImageCaptionService实现
 *
 * <AUTHOR>
 * @version ImageCaptionService.java v 0.1 2025-07-25 11:33:21
 */
@Slf4j
@Service
public class ImageCaptionServiceImpl implements ImageCaptionService {

    /** DAO */
    @Autowired
    private ImageCaptionDAO imageCaptionDAO;

    @Autowired
    private AliyunEmbeddingService aliyunEmbeddingService;

    @Override
    public ImageCaptionVO selectById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        ImageCaptionDO data = imageCaptionDAO.selectByPrimaryKeyWithLogicalDelete(id, false);
        if (null == data) {
            return null;
        }

        return ImageCaptionConverter.do2VO(data);
    }

    @Override
    public void deleteById(Integer id) {
        AssertUtil.assertNotNull(id, ResultCode.PARAM_INVALID, "id is null");

        int n = imageCaptionDAO.logicalDeleteByPrimaryKey(id);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "删除ImageCaption失败");
    }

    @Override
    public ImageCaptionVO insert(ImageCaptionVO imageCaption) {
        AssertUtil.assertNotNull(imageCaption, ResultCode.PARAM_INVALID, "imageCaption is null");
        AssertUtil.assertTrue(imageCaption.getId() == null, ResultCode.PARAM_INVALID, "imageCaption.id is present");

        // 创建时间、修改时间兜底
        if (imageCaption.getCreateTime() == null) {
            imageCaption.setCreateTime(new Date());
        }

        if (imageCaption.getModifyTime() == null) {
            imageCaption.setModifyTime(new Date());
        }

        ImageCaptionDO data = ImageCaptionConverter.vo2DO(imageCaption);
        // 逻辑删除字段初始化（虽然一般表字段有默认值）
        data.setDeleted(false);
        Integer n = imageCaptionDAO.insert(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "创建ImageCaption失败");
        AssertUtil.assertNotNull(data.getId(), "新建ImageCaption返回id为空");
        imageCaption.setId(data.getId());
        return imageCaption;
    }

    @Override
    public void updateByIdSelective(ImageCaptionVO imageCaption) {
        AssertUtil.assertNotNull(imageCaption, ResultCode.PARAM_INVALID, "imageCaption is null");
        AssertUtil.assertTrue(imageCaption.getId() != null, ResultCode.PARAM_INVALID, "imageCaption.id is null");

        // 使用反射验证所有PGvector类型的字段
        AssertUtil.validateDimensionOfNonNullPGVectorFields(imageCaption);

        // 修改时间必须更新
        imageCaption.setModifyTime(new Date());
        ImageCaptionDO data = ImageCaptionConverter.vo2DO(imageCaption);
        // 逻辑删除标过滤
        data.setDeleted(false);
        int n = imageCaptionDAO.updateByPrimaryKeySelective(data);
        AssertUtil.assertTrue(n == 1, ResultCode.BIZ_FAIL, "更新ImageCaption失败，影响行数:" + n);
    }

    @Override
    public List<ImageCaptionVO> queryImageCaptionList(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);

        List<ImageCaptionDO> list = imageCaptionDAO.selectByExample(example);
        return ImageCaptionConverter.doList2VOList(list);
    }

    @Override
    public Long queryImageCaptionCount(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);
        return imageCaptionDAO.countByExample(example);
    }

    /**
     * 带条件分页查询图像标注
     */
    @Override
    public PageInfo<ImageCaptionVO> queryImageCaptionByPage(ImageCaptionQuery query) {
        AssertUtil.assertNotNull(query, ResultCode.PARAM_INVALID, "query is null");

        AssertUtil.assertTrue(query.getPageNum() != null && query.getPageSize() != null && query.getPageNum() >= 1
                        && query.getPageSize() >= 1, ResultCode.PARAM_INVALID,
                "pageNum or pageSize is invalid,pageNum:"
                        + query.getPageNum() + ",pageSize:" + query.getPageSize());

        PageInfo<ImageCaptionVO> page = new PageInfo<>();

        ImageCaptionExample example = ImageCaptionConverter.query2Example(query);
        long totalCount = imageCaptionDAO.countByExample(example);
        if (totalCount == 0) {
            page.setList(new ArrayList<>());
            page.setSize(0);
            page.setTotalCount(0);
            page.setHasNextPage(false);

            return page;
        }

        List<ImageCaptionDO> list = imageCaptionDAO.selectByExample(example);
        page.setList(ImageCaptionConverter.doList2VOList(list));
        page.setSize(CollectionUtils.size(list));
        page.setTotalCount(totalCount);
        page.setHasNextPage(totalCount > (example.getOffset() + example.getRows()));

        return page;
    }

    /**
     * 根据款式向量相似度查询图像打标
     */
    @Override
    public List<ImageCaptionVO> queryByStyleVectorSimilarity(PGvector styleVector, ClothGenderEnum gender,
                                                             double similarityThreshold, int limit, String genre) {

        AssertUtil.assertNotNull(styleVector, ResultCode.PARAM_INVALID, "styleVector is null");
        AssertUtil.assertNotNull(gender, ResultCode.PARAM_INVALID, "gender is null or blank");
        AssertUtil.assertTrue(similarityThreshold >= 0 && similarityThreshold <= 1,
                ResultCode.PARAM_INVALID, "similarityThreshold must be between 0 and 1");
        AssertUtil.assertTrue(limit > 0, ResultCode.PARAM_INVALID, "limit must be greater than 0");

        List<ImageCaptionDO> matchedImages = imageCaptionDAO.selectByStyleVector(styleVector,gender.getCode(),similarityThreshold, limit, genre);
        return ImageCaptionConverter.doList2VOList(matchedImages);
    }

    @Override
    public PGvector calcClothTextVector(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType) {
        String clothFeatureText = this.getClothFeatureTextByAnalysis(clothAnalysis, clothType);
        AssertUtil.assertNotBlank(clothFeatureText, ResultCode.PARAM_INVALID, "clothFeatureText is null or blank");
        return aliyunEmbeddingService.getTextEmbeddingByMultiModalModel(clothFeatureText);
    }

    /**
     * 根据结构化描述构造服装文本
     *
     * @param clothAnalysis 服装分析数据
     * @param clothType     服装类型
     * @return 格式化的服装描述文本
     */
    @Override
    public String getClothFeatureTextByAnalysis(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType) {
        if (clothAnalysis == null) {
            log.error("服装分析数据为空，无法构造服装文本");
            return "";
        }

        if (clothType == null) {
            log.error("服装类型为空，无法构造服装文本");
            return "";
        }

        ImageAnalysisCaption.Clothing clothing = clothAnalysis.getClothing();
        List<String> descriptionParts = new ArrayList<>();

        // 根据服装类型决定提取哪些信息
        String targetClothingType = mapClothTypeToTarget(clothType);

        // --- 提取上衣信息 ---
        if (("Top".equals(targetClothingType) || "Suit".equals(targetClothingType)) &&
                clothing.getTop() != null && isValidValue(clothing.getTop().getType())) {

            ImageAnalysisCaption.Top top = clothing.getTop();
            List<String> topDescriptionElements = new ArrayList<>();

            if (isValidValue(top.getColor())) {
                topDescriptionElements.add("a " + top.getColor() + "-colored");
            }
            if (isValidValue(top.getLength())) {
                topDescriptionElements.add(top.getLength());
            }
            if (isValidValue(top.getType())) {
                topDescriptionElements.add(top.getType());
            }
            if (isValidValue(top.getFit())) {
                topDescriptionElements.add(", with a " + top.getFit() + " fit");
            }
            if (isValidValue(top.getSleeveLength())) {
                topDescriptionElements.add(", " + top.getSleeveLength());
            }
            if (isValidValue(top.getStyle())) {
                topDescriptionElements.add(", style description: " + top.getStyle());
            }
            if (isValidValue(top.getPatternAndFeature())) {
                topDescriptionElements.add(", features: " + top.getPatternAndFeature());
            }

            if (!topDescriptionElements.isEmpty()) {
                descriptionParts.add("Top: " + String.join("", topDescriptionElements));
            }
        }

        // --- 提取下装信息 ---
        if (("Bottom".equals(targetClothingType) || "Suit".equals(targetClothingType)) &&
                clothing.getBottom() != null && isValidValue(clothing.getBottom().getType())) {

            ImageAnalysisCaption.Bottom bottom = clothing.getBottom();
            List<String> bottomDescriptionElements = new ArrayList<>();

            if (isValidValue(bottom.getColor())) {
                bottomDescriptionElements.add("a " + bottom.getColor() + "-colored");
            }
            if (isValidValue(bottom.getLength())) {
                bottomDescriptionElements.add(bottom.getLength());
            }
            if (isValidValue(bottom.getType())) {
                bottomDescriptionElements.add(bottom.getType());
            }
            if (isValidValue(bottom.getStyle())) {
                bottomDescriptionElements.add(", style description: " + bottom.getStyle());
            }
            if (isValidValue(bottom.getPatternAndFeature())) {
                bottomDescriptionElements.add(", features: " + bottom.getPatternAndFeature());
            }

            if (!bottomDescriptionElements.isEmpty()) {
                descriptionParts.add("Bottom: " + String.join("", bottomDescriptionElements));
            }
        }

        // 返回结果
        if (descriptionParts.isEmpty()) {
            log.error("No information found for the specified clothing type: {}.", targetClothingType);
            return null;
        } else {
            return String.join(" ", descriptionParts);
        }
    }

    @Override
    public String getClothStyleDescription(ImageAnalysisCaption captionModel) {

        // 从结构化描述中提取款式相关字段
        if (captionModel == null) {
            return "";
        }

        StringBuilder styleDesc = new StringBuilder();
        if (captionModel.getClothing().getTop() != null) {
            ImageAnalysisCaption.Top top = captionModel.getClothing().getTop();
            styleDesc.append("Top: ").append(top.getStyle()).append(" ");
        }

        if (captionModel.getClothing().getBottom() != null) {
            ImageAnalysisCaption.Bottom bottom = captionModel.getClothing().getBottom();
            styleDesc.append("Bottom: ").append(bottom.getStyle()).append(" ");
        }

        return styleDesc.toString().trim();
    }

    /**
     * 将 ClothTypeEnum 映射为目标服装类型字符串
     */
    private String mapClothTypeToTarget(ClothTypeEnum clothType) {
        if (clothType == null) {
            return null;
        }

        switch (clothType) {
            case Tops:
                return "Top";
            case Bottoms:
                return "Bottom";
            case TwoPiece:
            case SwimSuit:
            case OnePiece:
            case SexyLingerie:
                return "Suit";
            default:
                return null;
        }
    }

    /**
     * 检查值是否有效（非空且不为"None"）
     */
    private boolean isValidValue(String value) {
        return value != null && !value.trim().isEmpty() && !"None".equalsIgnoreCase(value) && !"null".equalsIgnoreCase(value);
    }
}