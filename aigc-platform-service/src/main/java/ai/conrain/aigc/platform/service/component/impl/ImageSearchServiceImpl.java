package ai.conrain.aigc.platform.service.component.impl;

import ai.conrain.aigc.platform.integration.ai.SortModelEmbeddingService;
import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.integration.ai.model.StyleImageEmbeddingResult;
import ai.conrain.aigc.platform.integration.aliyun.AliyunEmbeddingService;
import ai.conrain.aigc.platform.integration.utils.BeanUtils;
import ai.conrain.aigc.platform.service.component.ImageCaptionService;
import ai.conrain.aigc.platform.service.component.ImageSearchService;
import ai.conrain.aigc.platform.service.component.ImageService;
import ai.conrain.aigc.platform.service.component.agent.CachedPCA;
import ai.conrain.aigc.platform.service.component.agent.SortModelService;
import ai.conrain.aigc.platform.service.enums.ClothShootGenreEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.biz.agent.ClothDescription;
import ai.conrain.aigc.platform.service.model.biz.agent.ClothShootStyleImageRecommendationParam;
import ai.conrain.aigc.platform.service.model.biz.agent.ImageMatchScope;
import ai.conrain.aigc.platform.service.model.biz.agent.ProcessedInput;
import ai.conrain.aigc.platform.service.model.biz.agent.StyleImageCandidate;
import ai.conrain.aigc.platform.service.model.biz.agent.UserRefImgDescription;
import ai.conrain.aigc.platform.service.model.biz.agent.UserUploadStyleImg;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import ai.conrain.aigc.platform.service.model.vo.ImageVO;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendResult;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendation;
import ai.conrain.aigc.platform.service.model.vo.StyleImageRecommendationBgCluster;
import ai.conrain.aigc.platform.service.util.AssertUtil;
import ai.conrain.aigc.platform.service.util.VectorUtil;
import com.pgvector.PGvector;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.tribuo.Dataset;
import org.tribuo.Example;
import org.tribuo.Model;
import org.tribuo.MutableDataset;
import org.tribuo.Prediction;
import org.tribuo.clustering.ClusterID;
import org.tribuo.clustering.ClusteringFactory;
import org.tribuo.clustering.hdbscan.HdbscanTrainer;
import org.tribuo.impl.ArrayExample;
import org.tribuo.provenance.DataSourceProvenance;
import org.tribuo.provenance.SimpleDataSourceProvenance;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ImageSearchServiceImpl implements ImageSearchService {

    @Autowired
    private AliyunEmbeddingService aliyunEmbeddingService;

    @Autowired
    private ImageCaptionService imageCaptionService;

    @Autowired
    private ImageService imageService;

    @Autowired
    private SortModelEmbeddingService sortModelEmbeddingService;

    // 线程池
    private ExecutorService executorService;

    @Autowired
    private SortModelService sortModelService;

    @Autowired
    private CachedPCA cachedPca;

    private static final int TEXT_EMB_DIMENSION = 256;

    /**
     * 初始化ONNX模型和线程池
     */
    @PostConstruct
    public void init() {
        try {
            // 初始化线程池
            initThreadPool();
        } catch (Exception e) {
            log.error("初始化失败", e);
            throw new RuntimeException("初始化失败", e);
        }
    }

    private void initThreadPool() {
        // 使用固定大小的线程池，线程数为CPU核心数的2倍，适合IO密集型任务
        int threadPoolSize = Math.max(4, Runtime.getRuntime().availableProcessors() * 2);
        executorService = Executors.newFixedThreadPool(threadPoolSize, r -> {
            Thread thread = new Thread(r);
            thread.setName("image-search-pool-" + thread.getId());
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        });
        log.info("线程池初始化成功，线程数: {}", threadPoolSize);
    }

    /**
     * 销毁ONNX模型资源和线程池
     */
    @PreDestroy
    public void destroy() {
        try {
            shutdownThreadPool();
        } catch (Exception e) {
            log.error("资源释放失败", e);
        }
    }

    private void shutdownThreadPool() {
        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown(); // 优雅关闭
            try {
                // 等待60秒让正在执行的任务完成
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow(); // 强制关闭
                    // 再等待60秒
                    if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                        log.warn("线程池未能完全关闭");
                    }
                }
                log.info("线程池已关闭");
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
                log.warn("线程池关闭过程中被中断", e);
            }
        }
    }

    /**
     * 计算打标向量
     *
     * @param imageCaptionVO
     * @param skipSortEmbedding
     */
    @Override
    public void calcEmbeddingsByImageCaption(ImageCaptionVO imageCaptionVO, ClothTypeEnum clothTypeEnum,
            boolean skipSortEmbedding) {
        AssertUtil.assertNotNull(imageCaptionVO, "imageCaptionVO is null");
        AssertUtil.assertNotNull(imageCaptionVO.getCaption(), "imageCaptionVO.getCaption() is null");
        AssertUtil.assertNotNull(imageCaptionVO.getImageId(), "imageCaptionVO.getImageId() is null");

        ImageVO imageVO = imageService.selectById(imageCaptionVO.getImageId());
        AssertUtil.assertNotNull(imageVO, "imageVO is null");
        AssertUtil.assertNotBlank(imageVO.getUrl(), "imageVO.getUrl() is null");

        ImageCaptionVO target = new ImageCaptionVO();
        target.setId(imageCaptionVO.getId());

        ImageAnalysisCaption caption = imageCaptionVO.getCaption();

        // 图像向量（组），由阿九提供的api，计算8个向量，用于排序模型的输入
        if (!skipSortEmbedding) {
            String sortEmbTaskId = String.format("IM_EMB_%d_%s", imageCaptionVO.getId(), System.currentTimeMillis());
            StyleImageEmbeddingResult styleImageEmbeddingResult = sortModelEmbeddingService
                    .calcStyleImageEmbeddings(imageVO.getUrl(), sortEmbTaskId, caption);

            target.setImgEmb(VectorUtil
                    .createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getOptImageEmbedding(), 1024));
            target.setBgImgEmb(VectorUtil.createVectorOrZeroIfNull(
                    styleImageEmbeddingResult.getEmbeddings().getBackgroundImageEmbedding(), 1024));
            target.setModelFacialImgEmb(
                    VectorUtil.createVectorOrZeroIfNull(
                            styleImageEmbeddingResult.getEmbeddings().getExpressionImageEmbedding(), 1024));
            target.setModelPoseImgEmb(VectorUtil
                    .createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getPoseImageEmbedding(), 1024));
            target.setSortBgTextEmb(
                    VectorUtil.createVectorOrZeroIfNull(
                            styleImageEmbeddingResult.getEmbeddings().getBackgroundImageEmbedding(), 1024));
            target.setSortFacialExpressionTextEmb(
                    VectorUtil.createVectorOrZeroIfNull(
                            styleImageEmbeddingResult.getEmbeddings().getExpressionTextEmbedding(), 1024));
            target.setSortAccessoriesTextEmb(
                    VectorUtil.createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getMatTextEmbedding(),
                            1024));
            target.setSortPoseTextEmb(VectorUtil
                    .createVectorOrZeroIfNull(styleImageEmbeddingResult.getEmbeddings().getPoseTextEmbedding(), 1024));
        }

        // 服装描述文本向量
        if (caption.getClothing() != null) {
            try {
                PGvector clothVector = imageCaptionService.calcClothTextVector(caption, clothTypeEnum);
                target.setClothTextEmb(clothVector);
            } catch (Exception e) {
                log.error("计算服装描述文本向量失败", e);
            }
        }

        // 批量计算文本向量以提高效率
        List<String> textsToEmbed = new ArrayList<>();
        List<String> textTypes = new ArrayList<>();

        // 收集需要向量化的文本
        // 款式向量
        if (caption.getClothing() != null) {
            String styleText = imageCaptionService.getClothStyleDescription(caption);
            if (StringUtils.isNotBlank(styleText)) {
                textsToEmbed.add(styleText);
                textTypes.add("style");
            }
        }

        // 背景道具文本向量
        if (caption.getShootingTheme() != null
                && StringUtils.isNotBlank(caption.getShootingTheme().getShootingScene())) {
            textsToEmbed.add(caption.getShootingTheme().getShootingScene());
            textTypes.add("bg");
        }

        // 配饰搭配文本向量
        if (caption.getClothing() != null && StringUtils.isNotBlank(caption.getClothing().getAccessories())) {
            textsToEmbed.add(caption.getClothing().getAccessories());
            textTypes.add("accessories");
        }

        // 发型文本向量
        if (caption.getModel() != null && StringUtils.isNotBlank(caption.getModel().getHairstyle())) {
            textsToEmbed.add(caption.getModel().getHairstyle());
            textTypes.add("hairstyle");
        }

        // 姿势文本向量
        if (caption.getModel() != null && StringUtils.isNotBlank(caption.getModel().getPosture())) {
            textsToEmbed.add(caption.getModel().getPosture());
            textTypes.add("pose");
        }

        // 批量调用阿里云服务计算向量
        if (!textsToEmbed.isEmpty()) {
            List<PGvector> embeddings = aliyunEmbeddingService.getEmbeddingByTexts(textsToEmbed, TEXT_EMB_DIMENSION);

            // 根据类型设置对应的向量
            for (int i = 0; i < textTypes.size() && i < embeddings.size(); i++) {
                String type = textTypes.get(i);
                PGvector vector = embeddings.get(i);

                switch (type) {
                    case "style":
                        target.setClothStyleTextEmb(vector);
                        break;
                    case "bg":
                        target.setBgTextEmb(vector);
                        break;
                    case "accessories":
                        target.setAccessoriesTextEmb(vector);
                        break;
                    case "hairstyle":
                        target.setHairstyleTextEmb(vector);
                        break;
                    case "pose":
                        target.setPoseTextEmb(vector);
                        break;
                }
            }
        }

        imageCaptionService.updateByIdSelective(target);
    }

    @Override
    public StyleImageRecommendResult searchAndRecommend(ClothShootStyleImageRecommendationParam param) {
        log.info("🎯 开始服装拍摄风格图片推荐算法");

        // 整体耗时统计开始
        long totalStartTime = System.currentTimeMillis();
        Map<String, Long> performanceMetrics = new LinkedHashMap<>();

        try {
            // 1. 输入处理模块，计算各种向量
            long step1StartTime = System.currentTimeMillis();
            ProcessedInput processedInput = processInput(param);
            long step1EndTime = System.currentTimeMillis();
            long step1Duration = step1EndTime - step1StartTime;
            performanceMetrics.put("输入处理模块", step1Duration);
            log.info("📥 输入处理完成 | 耗时:{}ms | 服装类型:{} | 参考图:{}张", 
                step1Duration, 
                param.getClothType().getDesc(),
                processedInput.getUserRefImgDescriptions().size());

            int quota = 2000;

            // 2. 服装图特征召回（100w->保留2000条）
            long step2StartTime = System.currentTimeMillis();
            List<StyleImageCandidate> candidates = recallByClothFeatures(processedInput, 0.5, quota);
            long step2EndTime = System.currentTimeMillis();
            long step2Duration = step2EndTime - step2StartTime;
            performanceMetrics.put("服装图特征召回", step2Duration);
            Map<String, Integer> recallGenreStats = getGenreStats(candidates);
            log.info("🔍 服装特征召回完成 | 耗时:{}ms | 候选数:{} | 流派分布:{}", 
                step2Duration, candidates.size(), recallGenreStats);

            // 3. 按服装辅字段和参考图打散和过滤（2000条->保留1000条）
            long step3StartTime = System.currentTimeMillis();
            List<StyleImageCandidate> diversifiedCandidates = diversifyByMMRWithWindow(candidates, processedInput, 0.5,
                    quota / 2, 10);
            long step3EndTime = System.currentTimeMillis();
            long step3Duration = step3EndTime - step3StartTime;
            performanceMetrics.put("MMR打散过滤", step3Duration);
            Map<String, Integer> filterGenreStats = getGenreStats(diversifiedCandidates);
            log.info("🎯 MMR打散过滤完成 | 耗时:{}ms | 输入:{} → 输出:{} | 流派分布:{}", 
                step3Duration, candidates.size(), diversifiedCandidates.size(), filterGenreStats);

            // 4. 搜索结果按流派与背景聚类
            long step4StartTime = System.currentTimeMillis();
            StyleImageRecommendResult result = clusterByGenreAndBackground(diversifiedCandidates, processedInput);
            long step4EndTime = System.currentTimeMillis();
            long step4Duration = step4EndTime - step4StartTime;
            performanceMetrics.put("流派背景聚类", step4Duration);
            log.info("🔗 流派背景聚类完成 | 耗时:{}ms | 输入:{} → 聚类结果:{}项", 
                step4Duration, diversifiedCandidates.size(), 
                result.getItems() != null ? result.getItems().size() : 0);

            // 计算总耗时
            long totalEndTime = System.currentTimeMillis();
            long totalDuration = totalEndTime - totalStartTime;
            performanceMetrics.put("总耗时", totalDuration);

            result.setPerformanceReport(logPerformanceReport(performanceMetrics, result));

            log.info("【耗时统计】:{}", result.getPerformanceReport());

            return result;

        } catch (Exception e) {
            long totalEndTime = System.currentTimeMillis();
            long totalDuration = totalEndTime - totalStartTime;
            log.error("服装拍摄风格图片推荐执行失败，总耗时：{}ms", totalDuration, e);
            throw new RuntimeException("推荐算法执行失败", e);
        }
    }

    /**
     * 获取流派统计信息
     */
    private Map<String, Integer> getGenreStats(List<StyleImageCandidate> candidates) {
        Map<String, Integer> tagCountMap = new HashMap<>();
        candidates.forEach(c -> {
            String genre = c.getGenreStr();
            if (StringUtils.isNotBlank(genre)) {
                String k = ClothShootGenreEnum.getByCode(genre).getDisplayName();
                tagCountMap.put(k, tagCountMap.getOrDefault(k, 0) + 1);
            }
        });
        return tagCountMap;
    }

    /**
     * 1. 输入处理模块（并行优化版本）
     */
    private ProcessedInput processInput(ClothShootStyleImageRecommendationParam param) throws Exception {
        AssertUtil.assertNotNull(param, "参数不能为空");
        AssertUtil.assertNotBlank(param.getClothImgUrl(), "服装图片URL不能为空");
        AssertUtil.assertNotNull(param.getClothType(), "服装类型不能为空");
        AssertUtil.assertNotNull(param.getClothGender(), "服装性别不能为空");
        AssertUtil.assertNotNull(param.getClothAnalysis(), "服装标注不能为空");

        ProcessedInput input = new ProcessedInput();
        long totalStartTime = System.currentTimeMillis();

        // 1.1 并行处理：服装图片向量计算 & 参考图片向量计算
        CompletableFuture<ClothDescription> clothFuture = CompletableFuture.supplyAsync(() -> {
            long clothProcessStartTime = System.currentTimeMillis();
            ClothDescription clothDescription = processClothImage(param);
            long clothProcessEndTime = System.currentTimeMillis();
            log.info("【耗时统计】服装图片处理完成，耗时：{}ms", clothProcessEndTime - clothProcessStartTime);
            return clothDescription;
        }, executorService);

        CompletableFuture<List<UserRefImgDescription>> refImagesFuture = CompletableFuture.supplyAsync(() -> {
            long refProcessStartTime = System.currentTimeMillis();
            List<UserRefImgDescription> userRefImgDescriptions = getUserUploadStyleImgDescriptionsParallel(
                    param);
            long refProcessEndTime = System.currentTimeMillis();
            log.info("【耗时统计】参考图片并行处理{}张图片，耗时：{}ms",
                    param.getUserUploadStyleImgs() != null ? param.getUserUploadStyleImgs().size() : 0,
                    refProcessEndTime - refProcessStartTime);
            return userRefImgDescriptions;
        }, executorService);

        // 等待服装图片和参考图片处理完成
        ClothDescription clothDescription = clothFuture.get();
        List<UserRefImgDescription> userRefImgDescriptions = refImagesFuture.get();

        input.setClothDescription(clothDescription);
        input.setUserRefImgDescriptions(userRefImgDescriptions);

        return input;
    }

    /**
     * 并行处理参考图片向量计算
     */
    @NotNull
    private List<UserRefImgDescription> getUserUploadStyleImgDescriptionsParallel(
            ClothShootStyleImageRecommendationParam param) {
        List<UserRefImgDescription> userRefImgDescriptions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getUserUploadStyleImgs())) {
            try {
                // 创建并行任务列表
                List<CompletableFuture<UserRefImgDescription>> futures = new ArrayList<>();

                // 为每个参考图创建并行处理任务
                for (int i = 0; i < param.getUserUploadStyleImgs().size(); i++) {
                    final int index = i;
                    UserUploadStyleImg styleImg = param.getUserUploadStyleImgs().get(index);
                    CompletableFuture<UserRefImgDescription> future = CompletableFuture
                            .supplyAsync(() -> processReferenceImageParallel(styleImg, index), executorService);
                    futures.add(future);
                }

                // 等待所有任务完成并收集结果
                for (CompletableFuture<UserRefImgDescription> future : futures) {
                    UserRefImgDescription result = future.get();
                    if (result != null) {
                        userRefImgDescriptions.add(result);
                    }
                }

            } catch (Exception e) {
                log.error("参考图片并行处理失败", e);
                throw new RuntimeException("参考图片并行处理失败", e);
            }
        }
        return userRefImgDescriptions;
    }

    /**
     * 处理单个参考图片（并行版本）
     */
    private UserRefImgDescription processReferenceImageParallel(UserUploadStyleImg styleImg, int index) {
        try {
            AssertUtil.assertNotNull(styleImg.getImgAnalysis(), ResultCode.PARAM_INVALID, "参考图片的视觉打标不能为空");
            AssertUtil.assertNotEmpty(styleImg.getImageMatchScopeList(), "imageMatchScopeList不能为空");

            // 统一处理：全局参考时，替换为4个子维度进行计算
            if (styleImg.getImageMatchScopeList().contains(ImageMatchScope.ALL)) {
                styleImg.getImageMatchScopeList().clear();
                styleImg.getImageMatchScopeList().addAll(Arrays.asList(ImageMatchScope.BG, ImageMatchScope.ACCESSORIES,
                        ImageMatchScope.MODEL_FACIAL, ImageMatchScope.MODEL_POSE));
            }

            // 收集当前参考图的所有维度文本
            List<String> currentImgTexts = new ArrayList<>();
            List<ImageMatchScope> currentImgScopes = new ArrayList<>();

            for (ImageMatchScope scope : styleImg.getImageMatchScopeList()) {
                String dimensionText = extractDimensionText(styleImg.getImgAnalysis(), scope);
                if (StringUtils.isNotBlank(dimensionText)) {
                    currentImgTexts.add(dimensionText);
                    currentImgScopes.add(scope);
                }
            }

            // 为当前参考图计算向量（每次调用不超过10个文本）
            Map<String, PGvector> dimensionVectorMap = new HashMap<>();
            if (!currentImgTexts.isEmpty()) {
                List<PGvector> currentImgVectors = aliyunEmbeddingService.getEmbeddingByTexts(currentImgTexts, TEXT_EMB_DIMENSION);

                // 构建维度向量映射
                for (int j = 0; j < currentImgScopes.size(); j++) {
                    dimensionVectorMap.put(currentImgScopes.get(j).name(), currentImgVectors.get(j));
                }
            }

            // 创建参考图描述对象
            UserRefImgDescription refImgDescription = BeanUtils.deepCopy(styleImg, UserRefImgDescription.class);
            refImgDescription.setDimensionVectors(dimensionVectorMap);

            return refImgDescription;
        } catch (Exception e) {
            log.error("处理参考图片{}时发生异常", index, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 服装图片理解
     */
    private ClothDescription processClothImage(ClothShootStyleImageRecommendationParam param) {
        try {
            ClothDescription description = BeanUtils.deepCopy(param, ClothDescription.class);
            // 并行计算3个向量
            CompletableFuture<PGvector> clothTextVectorFuture = CompletableFuture.supplyAsync(
                    () -> imageCaptionService.calcClothTextVector(param.getClothAnalysis(), param.getClothType()),
                    executorService);
            CompletableFuture<PGvector> styleTextVectorFuture = CompletableFuture.supplyAsync(() -> {
                String styleDescription = imageCaptionService.getClothStyleDescription(param.getClothAnalysis());
                return aliyunEmbeddingService.getEmbeddingByText(styleDescription, TEXT_EMB_DIMENSION);
            }, executorService);

            CompletableFuture<PGvector> clothImgVectorFuture = CompletableFuture.supplyAsync(
                    () -> aliyunEmbeddingService.getImgEmbeddingByMultiModalModel(param.getClothImgUrl()),
                    executorService);

            // 等待所有任务完成并设置结果
            description.setClothTextVector(clothTextVectorFuture.get());
            description.setStyleTextVector(styleTextVectorFuture.get());
            description.setClothImgVector(clothImgVectorFuture.get());

            return description;
        } catch (Exception e) {
            log.error("服装图片并行处理失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 2. 服装图特征召回（100w->保留2000条）
     */
    private List<StyleImageCandidate> recallByClothFeatures(ProcessedInput input, Double similarity, int limit) {
        List<StyleImageCandidate> candidates = new ArrayList<>();

        // 2.1 向量相似度查询
        long vectorQueryStartTime = System.currentTimeMillis();
        List<ImageCaptionVO> matchedImages = imageCaptionService.queryByStyleVectorSimilarity(
                input.getClothDescription().getStyleTextVector(),
                input.getClothDescription().getClothGender(),
                similarity,
                limit, ClothShootGenreEnum.SOCIAL_NETWORK.getCode());

        // 2.2 候选项处理和维度向量构建
        for (ImageCaptionVO imageCaption : matchedImages) {
            // 解析图像标注
            ImageAnalysisCaption captionModel = imageCaption.getCaption();
            if (captionModel == null) {
                continue;
            }

            StyleImageCandidate candidate = new StyleImageCandidate();
            candidate.setImageCaption(imageCaption);
            candidate.setStyleSimilarity(imageCaption.getClothStyleSimilarity());

            // 计算其他维度向量
            buildDimensionVectorsMap(candidate);

            candidates.add(candidate);
        }
        long candidateProcessEndTime = System.currentTimeMillis();

        log.info("【耗时统计】服装图特征召回完成，候选数量：{}，总耗时：{}ms",
                candidates.size(), candidateProcessEndTime - vectorQueryStartTime);
        return candidates;
    }

    /**
     * 带滑动窗口的MMR算法打散和过滤
     *
     * @param candidates 候选列表
     * @param input      处理后的输入
     * @param lambda     相关性与多样性的权衡参数
     * @param limit      目标选择数量
     * @param windowSize 滑动窗口大小，控制多样性计算的范围
     */
    private List<StyleImageCandidate> diversifyByMMRWithWindow(List<StyleImageCandidate> candidates,
            ProcessedInput input, Double lambda, int limit, int windowSize) {

        long mmrStartTime = System.currentTimeMillis();
        List<StyleImageCandidate> selectedCandidates = new ArrayList<>();
        List<StyleImageCandidate> remainingCandidates = new ArrayList<>(candidates);

        int targetSize = Math.min(limit, candidates.size());
        log.info("【耗时统计】滑动窗口MMR算法开始，目标选择{}条，当前候选{}条，窗口大小：{}", targetSize, candidates.size(), windowSize);

        // 3.1 MMR迭代选择过程
        int iterationCount = 0;

        while (selectedCandidates.size() < targetSize && !remainingCandidates.isEmpty()) {
            iterationCount++;
            StyleImageCandidate bestCandidate = null;
            double bestMMRScore = Double.NEGATIVE_INFINITY;

            for (StyleImageCandidate candidate : remainingCandidates) {
                // 计算相关性得分
                double relevanceScore = calculateRelevanceScore(candidate, input);

                // 计算滑动窗口内的多样性惩罚
                double diversityPenalty = calculateDiversityPenaltyWithWindow(candidate, selectedCandidates, input,
                        windowSize);

                // 计算MMR得分
                double mmrScore = lambda * relevanceScore - (1 - lambda) * diversityPenalty;

                if (mmrScore > bestMMRScore) {
                    bestMMRScore = mmrScore;
                    bestCandidate = candidate;
                }
            }

            if (bestCandidate != null) {
                selectedCandidates.add(bestCandidate);
                remainingCandidates.remove(bestCandidate);
            }
        }

        long mmrEndTime = System.currentTimeMillis();

        log.info("【耗时统计】滑动窗口MMR算法完成，总迭代{}次，选中{}条，窗口大小：{}，总耗时：{}ms",
                iterationCount, selectedCandidates.size(), windowSize, mmrEndTime - mmrStartTime);

        return selectedCandidates;
    }

    /**
     * 计算相关性得分（优化版本：使用预计算的平均向量）
     */
    private double calculateRelevanceScore(StyleImageCandidate candidate, ProcessedInput input) {
        // 相关性 = 款式相似度×0.5 + 参考图相关性×0.5
        double styleRelevance = candidate.getStyleSimilarity();

        double referenceRelevance = 0.0;
        if (input.getAverageDimensionVectors() != null && !input.getAverageDimensionVectors().isEmpty()) {
            double totalWeight = 0.0;
            double weightedSum = 0.0;

            // 使用预计算的平均向量，避免遍历所有参考图
            for (Map.Entry<String, PGvector> entry : input.getAverageDimensionVectors().entrySet()) {
                String dimensionName = entry.getKey();
                PGvector averageVector = entry.getValue();

                double weight = input.getDynamicWeights().getOrDefault(dimensionName, 0.0);
                if (weight > 0) {
                    PGvector candidateVector = candidate.getDimensionVectors().get(dimensionName);
                    if (candidateVector != null) {
                        double similarity = VectorUtil.innerProduct(candidateVector, averageVector);
                        weightedSum += weight * similarity;
                        totalWeight += weight;
                    }
                }
            }

            if (totalWeight > 0) {
                referenceRelevance = weightedSum / totalWeight;
            }
        }

        return 0.5 * styleRelevance + 0.5 * referenceRelevance;
    }

    /**
     * 计算滑动窗口内的多样性惩罚
     *
     * @param candidate          当前候选项
     * @param selectedCandidates 已选择的候选项列表
     * @param input              处理后的输入
     * @param windowSize         滑动窗口大小
     * @return 多样性惩罚值
     */
    private double calculateDiversityPenaltyWithWindow(StyleImageCandidate candidate,
            List<StyleImageCandidate> selectedCandidates, ProcessedInput input, int windowSize) {
        if (selectedCandidates.isEmpty()) {
            return 0.0;
        }

        // 确定滑动窗口的范围：取最近选择的windowSize个候选项
        int startIndex = Math.max(0, selectedCandidates.size() - windowSize);
        List<StyleImageCandidate> windowCandidates = selectedCandidates.subList(startIndex, selectedCandidates.size());

        double maxSimilarity = 0.0;

        // 只与窗口内的候选项计算相似度
        for (StyleImageCandidate selected : windowCandidates) {
            double similarity = calculateComprehensiveSimilarity(candidate, selected, input);
            maxSimilarity = Math.max(maxSimilarity, similarity);
        }

        return maxSimilarity;
    }

    /**
     * 4. 按流派与背景聚类
     */
    private StyleImageRecommendResult clusterByGenreAndBackground(List<StyleImageCandidate> candidates,
            ProcessedInput input) throws Exception {
        // 4.1 按流派分组
        long genreGroupStartTime = System.currentTimeMillis();
        Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups = groupByGenre(candidates, input);
        long genreGroupEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派分组完成，分为{}个流派，耗时：{}ms", genreGroups.size(), genreGroupEndTime - genreGroupStartTime);

        // 4.2 分配流派配额
        long quotaAllocStartTime = System.currentTimeMillis();
        Map<ClothShootGenreEnum, Integer> genreQuotas = allocateGenreQuotas(genreGroups, input,
                genreGroups.size() * 10); // todo 5个背景, 30个风格图,因此这里应该是 genreGroups.size() * 5 * 30
        long quotaAllocEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派配额分配完成，耗时：{}ms", quotaAllocEndTime - quotaAllocStartTime);

        // 4.3 流派内按背景聚类并排序截断（并行优化版本）
        long bgClusterStartTime = System.currentTimeMillis();
        List<List<StyleImageRecommendation>> resultItems = new ArrayList<>();

        // 创建并行任务列表
        List<CompletableFuture<List<StyleImageRecommendation>>> futures = new ArrayList<>();

        // 为每个流派创建并行处理任务
        for (Map.Entry<ClothShootGenreEnum, List<StyleImageCandidate>> entry : genreGroups.entrySet()) {
            ClothShootGenreEnum genre = entry.getKey();
            List<StyleImageCandidate> genreCandidates = entry.getValue();
            Integer quota = genreQuotas.get(genre);

            if (quota != null && quota > 0) {
                CompletableFuture<List<StyleImageRecommendation>> future = CompletableFuture.supplyAsync(() -> {
                    long singleGenreStartTime = System.currentTimeMillis();
                    try {
                        List<StyleImageRecommendation> genreItems = clusterByBgWithinGenre(genreCandidates, quota,
                                genre, input);
                        long singleGenreEndTime = System.currentTimeMillis();
                        log.info("【耗时统计】流派{}背景聚类完成，耗时：{}ms，生成{}个推荐",
                                genre.getDisplayName(), singleGenreEndTime - singleGenreStartTime,
                                genreItems != null ? genreItems.size() : 0);
                        return genreItems;
                    } catch (Exception e) {
                        log.error("流派{}背景聚类处理失败", genre.getDisplayName(), e);
                        return null;
                    }
                }, executorService);

                futures.add(future);
            }
        }

        // 等待所有任务完成并收集结果
        for (CompletableFuture<List<StyleImageRecommendation>> future : futures) {
            List<StyleImageRecommendation> genreItems = future.get();
            if (CollectionUtils.isNotEmpty(genreItems)) {
                resultItems.add(genreItems);
            }
        }

        long bgClusterEndTime = System.currentTimeMillis();
        log.info("【耗时统计】所有流派背景聚类并行处理完成，总耗时：{}ms，处理{}个流派",
                bgClusterEndTime - bgClusterStartTime, genreGroups.size());

        StyleImageRecommendResult result = new StyleImageRecommendResult();
        result.setItems(resultItems);

        return result;
    }

    private String extractDimensionText(ImageAnalysisCaption caption, ImageMatchScope scope) {
        // 根据维度提取对应文本
        if (caption == null) {
            throw new IllegalArgumentException("Invalid caption model");
        }

        switch (scope) {
            case BG:
                if (caption.getShootingTheme() != null) {
                    return caption.getShootingTheme().getShootingScene();
                }
                break;
            case ACCESSORIES:
                if (caption.getClothing() != null) {
                    return caption.getClothing().getAccessories();
                }
                break;
            case MODEL_FACIAL:
                if (caption.getModel() != null) {
                    return caption.getModel().getHairstyle() + "," + caption.getModel().getFacialExpression();
                }
                break;
            case MODEL_POSE:
                if (caption.getModel() != null) {
                    return caption.getModel().getPosture();
                }
                break;
            case ALL: {
                StringBuilder sb = new StringBuilder();
                List<ImageMatchScope> otherScopes = Arrays.asList(ImageMatchScope.BG, ImageMatchScope.ACCESSORIES,
                        ImageMatchScope.MODEL_FACIAL, ImageMatchScope.MODEL_POSE);
                otherScopes.forEach(s -> {
                    String text = extractDimensionText(caption, s);
                    if (StringUtils.isNotBlank(text)) {
                        sb.append(text).append(" ");
                    }
                });
                return sb.toString();
            }
            default:
                throw new IllegalArgumentException("Invalid image match scope: " + scope);
        }

        return "";
    }

    private void buildDimensionVectorsMap(StyleImageCandidate candidate) {
        // 计算候选图片的各维度向量
        Map<String, PGvector> dimensionVectors = new HashMap<>();

        // 获取背景向量
        if (candidate.getImageCaption().getBgTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.BG.name(), candidate.getImageCaption().getBgTextEmb());
        }

        // 获取配饰向量
        if (candidate.getImageCaption().getAccessoriesTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.ACCESSORIES.name(),
                    candidate.getImageCaption().getAccessoriesTextEmb());
        }

        // 获取发型向量
        if (candidate.getImageCaption().getHairstyleTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.MODEL_FACIAL.name(),
                    candidate.getImageCaption().getHairstyleTextEmb());
        }

        // 获取姿势向量
        if (candidate.getImageCaption().getPoseTextEmb() != null) {
            dimensionVectors.put(ImageMatchScope.MODEL_POSE.name(), candidate.getImageCaption().getPoseTextEmb());
        }

        candidate.setDimensionVectors(dimensionVectors);
    }

    private double calculateComprehensiveSimilarity(StyleImageCandidate candidate1, StyleImageCandidate candidate2,
            ProcessedInput input) {
        // 计算综合相似度
        double auxSimilarity = VectorUtil.innerProduct(
                candidate1.getImageCaption().getClothTextEmb(),
                candidate2.getImageCaption().getClothTextEmb());

        double totalWeight = 0.5; // 服装辅助特征权重
        double weightedSum = auxSimilarity * 0.5;

        // 使用预计算的平均向量优化计算参考图各维度的相似度
        if (input.getAverageDimensionVectors() != null && !input.getAverageDimensionVectors().isEmpty()) {
            for (Map.Entry<String, PGvector> entry : input.getAverageDimensionVectors().entrySet()) {
                String dimensionName = entry.getKey();
                double weight = input.getDynamicWeights().getOrDefault(dimensionName, 0.0);

                if (weight > 0) {
                    PGvector vec1 = candidate1.getDimensionVectors().get(dimensionName);
                    PGvector vec2 = candidate2.getDimensionVectors().get(dimensionName);

                    if (vec1 != null && vec2 != null) {
                        double similarity = VectorUtil.innerProduct(vec1, vec2);
                        weightedSum += weight * similarity;
                        totalWeight += weight;
                    }
                }
            }
        }

        return totalWeight > 0 ? weightedSum / totalWeight : 0.0;
    }

    private Map<ClothShootGenreEnum, List<StyleImageCandidate>> groupByGenre(List<StyleImageCandidate> candidates,
            ProcessedInput input) {
        // 按流派分组
        Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups = new HashMap<>();

        // 初始化默认流派组
        Set<ClothShootGenreEnum> defaultGenres = new HashSet<>();
        defaultGenres.add(ClothShootGenreEnum.LOOKBOOK);
        defaultGenres.add(ClothShootGenreEnum.FASHION_BLOCKBUSTER);
        defaultGenres.add(ClothShootGenreEnum.COMMERCIAL_PHOTO);
        defaultGenres.add(ClothShootGenreEnum.SOCIAL_NETWORK);

        // 确定扩展流派（用户参考图中的走秀和对镜自拍）
        Set<ClothShootGenreEnum> extendedGenres = new HashSet<>();
        if (CollectionUtils.isNotEmpty(input.getUserRefImgDescriptions())) {
            for (UserRefImgDescription refDesc : input.getUserRefImgDescriptions()) {
                ImageAnalysisCaption captionModel = refDesc.getImgAnalysis();
                if (captionModel != null && captionModel.getShootingTheme() != null
                        && StringUtils.isNotBlank(captionModel.getShootingTheme().getGenre())) {

                    String genreStr = captionModel.getShootingTheme().getGenre();
                    ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genreStr);
                    if (genreEnum == ClothShootGenreEnum.SHOW || genreEnum == ClothShootGenreEnum.MIRROR_SELFIE) {
                        extendedGenres.add(genreEnum);
                    }
                }
            }
        }

        // 初始化所有流派组
        Set<ClothShootGenreEnum> allGenres = new HashSet<>(defaultGenres);
        allGenres.addAll(extendedGenres);

        for (ClothShootGenreEnum genre : allGenres) {
            genreGroups.put(genre, new ArrayList<>());
        }

        // 将候选图片按流派分组
        for (StyleImageCandidate candidate : candidates) {
            ClothShootGenreEnum genreEnum = candidate.getGenreEnum();

            // 只有在允许的流派中才加入分组
            if (genreEnum != null && genreGroups.containsKey(genreEnum)) {
                genreGroups.get(genreEnum).add(candidate);
            }
        }

        return genreGroups;
    }

    private Map<ClothShootGenreEnum, Integer> allocateGenreQuotas(
            Map<ClothShootGenreEnum, List<StyleImageCandidate>> genreGroups, ProcessedInput input, Integer totalQuota) {
        // 分配流派配额
        Map<ClothShootGenreEnum, Integer> genreQuotas = new HashMap<>();

        // 统计各类流派数量
        Set<ClothShootGenreEnum> genres = new HashSet<>();
        genres.add(ClothShootGenreEnum.LOOKBOOK);
        genres.add(ClothShootGenreEnum.FASHION_BLOCKBUSTER);
        genres.add(ClothShootGenreEnum.COMMERCIAL_PHOTO);
        genres.add(ClothShootGenreEnum.SOCIAL_NETWORK);

        Set<ClothShootGenreEnum> extendedGenres = new HashSet<>();
        if (CollectionUtils.isNotEmpty(input.getUserRefImgDescriptions())) {
            for (UserRefImgDescription refDesc : input.getUserRefImgDescriptions()) {
                ImageAnalysisCaption caption = refDesc.getImgAnalysis();
                if (caption != null && caption.getShootingTheme() != null
                        && StringUtils.isNotBlank(caption.getShootingTheme().getGenre())) {
                    String genreStr = caption.getShootingTheme().getGenre();
                    ClothShootGenreEnum genreEnum = ClothShootGenreEnum.getByCode(genreStr);
                    if (genreEnum == ClothShootGenreEnum.SHOW || genreEnum == ClothShootGenreEnum.MIRROR_SELFIE) {
                        extendedGenres.add(genreEnum);
                    }
                }
            }
        }

        genres.addAll(extendedGenres);

        // 分配默认流派配额（各流派平分）
        for (ClothShootGenreEnum genre : genres) {
            if (genreGroups.containsKey(genre)) {
                genreQuotas.put(genre, totalQuota / genres.size());
            }
        }

        return genreQuotas;
    }

    private List<StyleImageRecommendation> clusterByBgWithinGenre(
            List<StyleImageCandidate> candidates, int quota, ClothShootGenreEnum genre, ProcessedInput input)
            throws Exception {

        long genreClusterStartTime = System.currentTimeMillis();
        log.info("【耗时统计】开始对流派{}进行背景聚类，候选图片数量：{}, 配额：{}", genre.getDisplayName(), candidates.size(), quota);

        // 4.3.1 计算匹配度评分（批量处理优化）
        long matchScoreStartTime = System.currentTimeMillis();
        sortModelService.calculateMatchScoresBatch(candidates, input);
        long matchScoreEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}匹配度批量计算完成，耗时：{}ms", genre.getDisplayName(), matchScoreEndTime - matchScoreStartTime);

        if (candidates.isEmpty()) {
            log.warn("流派{}经过匹配度过滤后无候选图片", genre.getDisplayName());
            return new ArrayList<>();
        }

        // 4.3.2 背景聚类
        long clusteringStartTime = System.currentTimeMillis();
        List<List<StyleImageCandidate>> backgroundClusters = performBackgroundClustering(candidates);
        long clusteringEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}背景聚类完成，生成{}个聚类，耗时：{}ms",
                genre.getDisplayName(), backgroundClusters.size(), clusteringEndTime - clusteringStartTime);

        if (backgroundClusters.isEmpty()) {
            log.warn("流派{}没有满足最小聚类大小要求的聚类", genre.getDisplayName());
            return new ArrayList<>();
        }

        // 4.3.4 聚类排序和配额分配
        long sortingStartTime = System.currentTimeMillis();
        List<StyleImageRecommendation> items = new ArrayList<>();

        // 计算每个聚类应分配的配额
        int totalClusters = backgroundClusters.size();
        int baseQuotaPerCluster = quota / totalClusters;
        int remainingQuota = quota % totalClusters;

        // 按聚类平均匹配度排序，匹配度高的聚类优先分配
        backgroundClusters.sort((cluster1, cluster2) -> {
            double avgScore1 = cluster1.stream().mapToDouble(StyleImageCandidate::getMatchScore).average().orElse(0.0);
            double avgScore2 = cluster2.stream().mapToDouble(StyleImageCandidate::getMatchScore).average().orElse(0.0);
            return Double.compare(avgScore2, avgScore1);
        });
        long sortingEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}聚类排序完成，耗时：{}ms", genre.getDisplayName(), sortingEndTime - sortingStartTime);

        // 4.3.5 从聚类中选择最佳候选
        long selectionStartTime = System.currentTimeMillis();
        for (int clusterIndex = 0; clusterIndex < backgroundClusters.size(); clusterIndex++) {
            List<StyleImageCandidate> cluster = backgroundClusters.get(clusterIndex);

            // 计算当前聚类的配额
            int clusterQuota = baseQuotaPerCluster;
            if (clusterIndex < remainingQuota) {
                clusterQuota++; // 前几个聚类多分配1个配额
            }

            // 对聚类内的候选图片按匹配度排序
            cluster.sort((a, b) -> Double.compare(b.getMatchScore(), a.getMatchScore()));

            // 从当前聚类中选择最佳候选
            int selectedFromCluster = 0;
            for (StyleImageCandidate candidate : cluster) {
                if (selectedFromCluster >= clusterQuota) {
                    break;
                }

                StyleImageRecommendation item = convertToRecommendationItem(candidate, genre,
                        clusterIndex);
                if (item != null) {
                    items.add(item);
                    selectedFromCluster++;
                }
            }

            log.debug("从聚类{}中选择了{}张图片", clusterIndex, selectedFromCluster);
        }
        long selectionEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}候选选择完成，耗时：{}ms", genre.getDisplayName(), selectionEndTime - selectionStartTime);

        // 4.3.6 最终排序
        long finalSortStartTime = System.currentTimeMillis();
        items.sort((a, b) -> {
            // 按聚类索引排序，确保同一聚类的图片在一起
            Integer clusterA = (a.getBackgroundCluster() != null) ? 0 : 0; // 简化排序逻辑
            Integer clusterB = (b.getBackgroundCluster() != null) ? 0 : 0;
            return Integer.compare(clusterA, clusterB);
        });
        long finalSortEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}最终排序完成，耗时：{}ms", genre.getDisplayName(), finalSortEndTime - finalSortStartTime);

        long genreClusterEndTime = System.currentTimeMillis();
        log.info("【耗时统计】流派{}背景聚类全流程完成，返回{}条结果，总耗时：{}ms",
                genre.getDisplayName(), items.size(), genreClusterEndTime - genreClusterStartTime);
        return items;
    }

    /**
     * 将StyleImageCandidate转换为ClothShootStyleImageRecommendationItem
     */
    private StyleImageRecommendation convertToRecommendationItem(
            StyleImageCandidate candidate, ClothShootGenreEnum genre, int backgroundClusterIndex) {
        try {
            StyleImageRecommendation item = new StyleImageRecommendation();
            item.setClothShootGenreEnum(genre);
            item.setImageId(candidate.getImageCaption().getImageId());

            // 创建背景聚类对象
            StyleImageRecommendationBgCluster backgroundCluster = new StyleImageRecommendationBgCluster();
            // 由于该类目前为空，我们暂时不设置任何属性
            item.setBackgroundCluster(backgroundCluster);

            // 获取图片信息
            ImageVO imageVO = imageService.selectById(candidate.getImageCaption().getImageId());
            if (imageVO != null) {
                item.setImageUrl(imageVO.getUrl());
                // 移除getShowUrl调用，因为ImageVO中没有这个方法
                item.setImageShowUrl(imageVO.getUrl()); // 使用相同的URL
            }

            return item;
        } catch (Exception e) {
            log.error("转换推荐项失败，候选图片ID：{}", candidate.getImageCaption().getImageId(), e);
            return null;
        }
    }

    /**
     * 执行背景聚类
     */
    private List<List<StyleImageCandidate>> performBackgroundClustering(List<StyleImageCandidate> candidates) {
        // 提取背景向量
        List<float[]> backgroundVectors = new ArrayList<>();
        List<StyleImageCandidate> validCandidates = new ArrayList<>();

        for (StyleImageCandidate candidate : candidates) {
            PGvector bgVector = candidate.getImageCaption().getBgTextEmb();
            if (bgVector != null) {
                backgroundVectors.add(bgVector.toArray());
                validCandidates.add(candidate);
            }
        }

        if (backgroundVectors.isEmpty()) {
            // 如果没有背景向量，按背景文本简单分组
            return groupByBackgroundText(candidates);
        }

        // PCA降维
        long t1 = System.currentTimeMillis();
        double[][] reducedVectors = cachedPca.pca(backgroundVectors, Math.min(128, backgroundVectors.get(0).length));
        log.info("PCA耗时：{}ms", System.currentTimeMillis() - t1);

        // HDBSCAN进行聚类
        return performHDBSCANClustering(validCandidates, reducedVectors);
    }

    private List<List<StyleImageCandidate>> performHDBSCANClustering(List<StyleImageCandidate> candidates,
            double[][] vectors) {
        Dataset<ClusterID> dataset = createTribuoDataset(vectors);
        HdbscanTrainer trainer = new HdbscanTrainer(
                5, // 最小聚类大小
                HdbscanTrainer.Distance.COSINE, // 距离函数
                5, // 计算核心距离的邻居数量
                4 // 并行计算线程数
        );

        // 训练HDBSCAN模型
        Model<ClusterID> model = trainer.train(dataset);

        // 获取聚类结果
        List<Prediction<ClusterID>> predictions = new ArrayList<>();
        for (Example<ClusterID> example : dataset) {
            predictions.add(model.predict(example));
        }

        // 将Tribuo聚类结果转换为我们的数据结构
        Map<Integer, List<StyleImageCandidate>> clusterMap = convertTribuoResults(predictions, candidates);

        // 过滤掉小于5个图片的簇
        clusterMap.entrySet().removeIf(entry -> entry.getValue().size() < 5);

        List<List<StyleImageCandidate>> clusters = new ArrayList<>(clusterMap.values());
        log.info("基于Tribuo库的HDBSCAN聚类完成，共生成{}个有效聚类", clusters.size());

        return clusters;
    }

    /**
     * 创建Tribuo数据集
     */
    private Dataset<ClusterID> createTribuoDataset(double[][] vectors) {
        ClusteringFactory factory = new ClusteringFactory();
        DataSourceProvenance provenance = new SimpleDataSourceProvenance("Generated clustering data", factory);
        MutableDataset<ClusterID> dataset = new MutableDataset<>(provenance, factory);

        for (int i = 0; i < vectors.length; i++) {
            double[] vector = vectors[i];

            // 使用 ArrayExample 创建示例
            ArrayExample<ClusterID> example = new ArrayExample<>(factory.getUnknownOutput(), 1.0f, vector.length);

            // 添加特征到示例
            for (int j = 0; j < vector.length; j++) {
                example.add("feature_" + j, vector[j]);
            }

            dataset.add(example);
        }

        return dataset;
    }

    /**
     * 将Tribuo聚类结果转换为我们的数据结构
     */
    private Map<Integer, List<StyleImageCandidate>> convertTribuoResults(
            List<Prediction<ClusterID>> predictions, List<StyleImageCandidate> candidates) {
        Map<Integer, List<StyleImageCandidate>> clusterMap = new HashMap<>();

        for (int i = 0; i < predictions.size() && i < candidates.size(); i++) {
            Prediction<ClusterID> prediction = predictions.get(i);
            ClusterID clusterId = prediction.getOutput();

            // 跳过噪声点（聚类ID为0的点被认为是噪声）
            if (clusterId.getID() != 0) {
                int clusterIdInt = clusterId.getID();
                clusterMap.computeIfAbsent(clusterIdInt, k -> new ArrayList<>()).add(candidates.get(i));
            }
        }

        return clusterMap;
    }


    /**
     * 按背景文本分组
     */
    private List<List<StyleImageCandidate>> groupByBackgroundText(List<StyleImageCandidate> candidates) {
        Map<String, List<StyleImageCandidate>> groups = new HashMap<>();

        for (StyleImageCandidate candidate : candidates) {
            String backgroundKey = extractBackgroundKey(candidate);
            groups.computeIfAbsent(backgroundKey, k -> new ArrayList<>()).add(candidate);
        }

        return new ArrayList<>(groups.values());
    }

    /**
     * 提取背景关键字
     */
    private String extractBackgroundKey(StyleImageCandidate candidate) {
        return StringUtils.defaultString(candidate.getShootingScene(), "default");
    }

    /**
     * 输出详细的耗时分析报告
     */
    private String logPerformanceReport(Map<String, Long> performanceMetrics, StyleImageRecommendResult result) {
        StringBuilder report = new StringBuilder();
        report.append("\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");
        report.append("【服装拍摄风格图片推荐算法 - 耗时分析报告】\n");
        report.append(StringUtils.repeat("=", 80)).append("\n");

        long totalTime = performanceMetrics.get("总耗时");

        // 1. 各环节耗时详情（按耗时排序）
        report.append("📊 各算法环节耗时详情：\n");
        performanceMetrics.entrySet().stream()
                .filter(entry -> !"总耗时".equals(entry.getKey()))
                .forEach(entry -> {
                    String stepName = entry.getKey();
                    Long duration = entry.getValue();
                    double percentage = (duration * 100.0) / totalTime;
                    String icon = percentage > 30 ? "🔴" : percentage > 15 ? "🟡" : "🟢";
                    report.append(
                            String.format("  %s %-15s: %6d ms (%5.1f%%)\n", icon, stepName, duration, percentage));
                });
        report.append(String.format("  └─ %-15s: %6d ms (100.0%%)\n", "总耗时", totalTime));

        // 2. 性能瓶颈识别
        report.append("\n🔍 性能瓶颈识别：\n");

        // 找出最耗时的环节
        String slowestStep = "";
        long maxDuration = 0;
        for (Map.Entry<String, Long> entry : performanceMetrics.entrySet()) {
            if (!"总耗时".equals(entry.getKey()) && entry.getValue() > maxDuration) {
                maxDuration = entry.getValue();
                slowestStep = entry.getKey();
            }
        }
        report.append(String.format("  • 最耗时环节：%s (%d ms)\n", slowestStep, maxDuration));

        // 6. 执行总结
        report.append("\n📋 执行总结：\n");
        report.append(String.format("  • 总执行时间：%d ms\n", totalTime));
        report.append(String.format("  • 主要瓶颈：%s\n", slowestStep));
        report.append(String.format("  • 执行时间：%s\n", new java.util.Date()));

        report.append(StringUtils.repeat("=", 80));

        return report.toString();
    }

}