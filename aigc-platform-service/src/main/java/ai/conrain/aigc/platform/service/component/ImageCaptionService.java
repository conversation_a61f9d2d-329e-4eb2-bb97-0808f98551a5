package ai.conrain.aigc.platform.service.component;

import ai.conrain.aigc.platform.integration.ai.model.ImageAnalysisCaption;
import ai.conrain.aigc.platform.service.enums.ClothGenderEnum;
import ai.conrain.aigc.platform.service.enums.ClothTypeEnum;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageCaptionVO;
import com.pgvector.PGvector;

import java.util.List;

/**
 * 图像标注 Service定义
 *
 * <AUTHOR>
 * @version ImageCaptionService.java v 0.1 2025-07-25 11:33:21
 */
public interface ImageCaptionService {
	
	/**
	 * 查询图像标注对象
	 * @param   id 主键
	 * @return 返回结果
	 */
	ImageCaptionVO selectById(Integer id);

	/**
	 * 删除图像标注对象
	 * @param id 主键
	 */
	void deleteById(Integer id);

	/**
	 * 添加图像标注对象
	 * @param imageCaption 对象参数
	 * @return 返回结果
	 */
	ImageCaptionVO insert(ImageCaptionVO imageCaption);

	/**
	 * 修改图像标注对象
	 * @param imageCaption 对象参数
	 */
	void updateByIdSelective(ImageCaptionVO imageCaption);

	/**
	 * 带条件批量查询图像标注列表
	 * @param query 查询条件
	 * return 结果
	 */
	List<ImageCaptionVO> queryImageCaptionList(ImageCaptionQuery query);

	/**
	 * 带条件查询图像标注数量
	 * @param query 查询条件
	 * return 记录条数
	 */
	Long queryImageCaptionCount(ImageCaptionQuery query);

	/**
	 * 带条件分页查询图像标注
	 * @param query 查询条件
	 * return 分页结果
	 */
	PageInfo<ImageCaptionVO> queryImageCaptionByPage(ImageCaptionQuery query);

	/**
     * 根据款式向量相似度查询图像打标列表
     *
     * @param styleVector         款式向量
     * @param gender              性别
     * @param similarityThreshold 相似度阈值
     * @param limit               限制返回数量
     * @param genre
     * @return 匹配的图像打标列表
     */
	List<ImageCaptionVO> queryByStyleVectorSimilarity(PGvector styleVector, ClothGenderEnum gender,
                                                      double similarityThreshold, int limit, String genre);

	/**
	 * 根据结构化描述构造服装文本
	 */
	String getClothFeatureTextByAnalysis(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType);

	/**
	 * 根据结构化描述构造服装款式文本（用于召回）
	 * @param captionModel
	 * @return
	 */
	String getClothStyleDescription(ImageAnalysisCaption captionModel);

	PGvector calcClothTextVector(ImageAnalysisCaption clothAnalysis, ClothTypeEnum clothType);
}