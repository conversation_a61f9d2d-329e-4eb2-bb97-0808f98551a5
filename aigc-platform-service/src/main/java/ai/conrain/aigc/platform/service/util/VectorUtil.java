package ai.conrain.aigc.platform.service.util;

import com.pgvector.PGvector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

@Slf4j
public class VectorUtil {

    public static PGvector createZeroVector(int dimension) {
        return new PGvector(new float[dimension]);
    }

    public static PGvector createVectorOrZeroIfNull(float[] vector, int dimension) {
        if (vector == null || vector.length != dimension) {
            return createZeroVector(dimension);
        }
        return new PGvector(vector);
    }

    public static Pair<String, String> pairOfText4AliyunEmbedding(String text) {
        return Pair.of("text", text);
    }

    public static Pair<String, String> pairOfImg4AliyunEmbedding(String imgUrl) {
        return Pair.of("image", imgUrl);
    }

    /**
     * 计算余弦相似度，注意，输入的两个向量应该是norm=1的标准化的单位向量
     * 使用向量化优化提升性能
     */
    public static double innerProduct(PGvector vector1, PGvector vector2) {
        if (vector1 == null || vector2 == null) {
            return 0.0;
        }

        float[] array1 = vector1.toArray();
        float[] array2 = vector2.toArray();

        if (array1.length != array2.length) {
            return 0.0;
        }

        return calculateDotProductVectorized(array1, array2);
    }

    /**
     * 向量化点积计算，使用循环展开和累加器优化
     */
    private static double calculateDotProductVectorized(float[] array1, float[] array2) {
        int length = array1.length;
        double sum1 = 0.0, sum2 = 0.0, sum3 = 0.0, sum4 = 0.0;

        // 8路循环展开，利用CPU的超标量执行
        int i = 0;
        int limit = length - 7;

        for (; i < limit; i += 8) {
            sum1 += array1[i] * array2[i] + array1[i + 1] * array2[i + 1];
            sum2 += array1[i + 2] * array2[i + 2] + array1[i + 3] * array2[i + 3];
            sum3 += array1[i + 4] * array2[i + 4] + array1[i + 5] * array2[i + 5];
            sum4 += array1[i + 6] * array2[i + 6] + array1[i + 7] * array2[i + 7];
        }

        // 处理剩余元素（4路展开）
        limit = length - 3;
        for (; i < limit; i += 4) {
            sum1 += array1[i] * array2[i] + array1[i + 1] * array2[i + 1];
            sum2 += array1[i + 2] * array2[i + 2] + array1[i + 3] * array2[i + 3];
        }

        // 处理剩余元素（2路展开）
        limit = length - 1;
        for (; i < limit; i += 2) {
            sum1 += array1[i] * array2[i] + array1[i + 1] * array2[i + 1];
        }

        // 处理最后一个元素（如果长度为奇数）
        if (i < length) {
            sum1 += array1[i] * array2[i];
        }

        return sum1 + sum2 + sum3 + sum4;
    }

    /**
     * 计算多个向量的平均值
     * @param vectors 向量列表
     * @return 平均向量
     */
    public static PGvector calculateAverageVector(List<PGvector> vectors) {
        if (vectors == null || vectors.isEmpty()) {
            throw new IllegalArgumentException("向量列表不能为空");
        }

        // 获取向量维度
        int dimension = vectors.get(0).toArray().length;
        
        // 验证所有向量维度一致
        for (PGvector vector : vectors) {
            if (vector.toArray().length != dimension) {
                throw new IllegalArgumentException("所有向量的维度必须一致");
            }
        }

        // 计算平均值
        float[] averageArray = new float[dimension];
        int vectorCount = vectors.size();

        for (PGvector vector : vectors) {
            float[] array = vector.toArray();
            for (int i = 0; i < dimension; i++) {
                averageArray[i] += array[i];
            }
        }

        // 除以向量数量得到平均值
        for (int i = 0; i < dimension; i++) {
            averageArray[i] /= vectorCount;
        }

        return new PGvector(averageArray);
    }
}
