package ai.conrain.aigc.platform.web.controller;

import ai.conrain.aigc.platform.service.component.ImageGroupCaptionUserService;
import ai.conrain.aigc.platform.service.model.query.ImageGroupCaptionUserQuery;
import ai.conrain.aigc.platform.service.model.vo.ImageGroupCaptionUserVO;
import com.alibaba.fastjson2.JSON;
import java.util.*;

import ai.conrain.aigc.platform.service.model.vo.ImageCaptionUserVO;
import ai.conrain.aigc.platform.service.model.query.ImageCaptionUserQuery;
import ai.conrain.aigc.platform.service.component.ImageCaptionUserService;
import ai.conrain.aigc.platform.service.model.common.PageInfo;
import ai.conrain.aigc.platform.service.model.common.Result;
import ai.conrain.aigc.platform.service.model.common.ResultCode;
import ai.conrain.aigc.platform.service.resolver.JsonArg;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ImageCaptionUser控制器
 *
 * <AUTHOR>
 * @version ImageCaptionUserService.java v 0.1 2025-07-30 08:19:29
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/image-gallery/imageCaptionUser")
public class ImageCaptionUserController {

	/** imageCaptionUserService */
	@Autowired
	private ImageCaptionUserService imageCaptionUserService;
    @Autowired
    private ImageGroupCaptionUserService imageGroupCaptionUserService;
	
	@GetMapping("/getById/{id}")
	public Result<ImageCaptionUserVO> getById(@NotNull @PathVariable("id")Integer id) {
		return Result.success(imageCaptionUserService.selectById(id));
	}
	
	@PostMapping("/create")
	public Result<Integer> create(@Valid @RequestBody ImageCaptionUserVO imageCaptionUser){
		return Result.success(imageCaptionUserService.insert(imageCaptionUser).getId());
	}

	@PostMapping("/save")
	public Result<Integer> save(@Valid @RequestBody ImageCaptionUserVO imageCaptionUser){
        if ("style".equals(imageCaptionUser.getType())) {
            ImageGroupCaptionUserVO imageGroupCaptionUserVO = new ImageGroupCaptionUserVO();
            imageGroupCaptionUserVO.setImageGroupId(imageCaptionUser.getImageId());
            imageGroupCaptionUserVO.setUserId(imageCaptionUser.getUserId());
            imageGroupCaptionUserVO.setCaption(JSON.toJSONString(imageCaptionUser.getCaption()));
            return Result.success(imageGroupCaptionUserService.save(imageGroupCaptionUserVO).getId());
        }
		return Result.success(imageCaptionUserService.save(imageCaptionUser).getId());
	}

	@PostMapping("/deleteById")
	public Result<?> deleteById(@NotNull @JsonArg Integer id) {
		imageCaptionUserService.deleteById(id);
		return Result.success();
	}

	@PostMapping("/updateById")
	public Result<?> updateByIdSelective(@Valid @RequestBody ImageCaptionUserVO imageCaptionUser){
		imageCaptionUserService.updateByIdSelective(imageCaptionUser);
		return Result.success();
	}

    @PostMapping("/queryLatest")
	public Result<ImageCaptionUserVO> queryLatest(@Valid @RequestBody ImageCaptionUserQuery query){
        ImageCaptionUserVO result = imageCaptionUserService.queryImageCaptionUserList(query)
            .stream()
            .max(Comparator.comparing(ImageCaptionUserVO::getModifyTime))
            .orElse(null);
		return Result.success(result);
	}

	@PostMapping("/queryById")
	public Result<ImageCaptionUserVO> queryById(@Valid @RequestBody ImageCaptionUserQuery query){
        if (query.getImageId() == null || query.getUserId() == null) {
            return Result.success(null);
        }
        List<ImageCaptionUserVO> list = imageCaptionUserService.queryImageCaptionUserList(query);
        return Result.success(CollectionUtils.isEmpty(list) ? null : list.getFirst());
	}

    @PostMapping("/queryList")
    public Result<List<ImageCaptionUserVO>> queryImageCaptionUserList(@Valid @RequestBody ImageCaptionUserQuery query){
        return Result.success(imageCaptionUserService.queryImageCaptionUserList(query));
    }
	
	@PostMapping("/queryByPage")
	public Result<PageInfo<ImageCaptionUserVO>> getImageCaptionUserByPage(@Valid @RequestBody ImageCaptionUserQuery query) {

		if (query == null || query.getPageSize() == null || query.getPageNum() == null || query.getPageSize() < 1 || query.getPageNum() < 1) {
			return Result.failedWithMessage(ResultCode.PARAM_INVALID, "翻页查询请求参数非法");
		}
		return Result.success(imageCaptionUserService.queryImageCaptionUserByPage(query));
	}
}
